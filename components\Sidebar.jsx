'use client';

import React from "react";
import {
  FaSignOutAlt,
} from "react-icons/fa";
import { IoHome } from "react-icons/io5";


const SidebarItem = ({ icon: Icon, label }) => {
  return (
    <div className="flex items-center gap-3 w-full px-4 py-2 font-medium text-sm">
      <div className="w-8 h-8 flex items-center justify-center rounded-lg bg-orange-600 text-white">
        <Icon size={16} />
      </div>
      <span className="text-gray-600 font-semibold">
        {label}
      </span>
    </div>
  );
};


const Sidebar = ({ onLogout }) => {
  return (
    <div className="h-full w-64 bg-white shadow-lg flex flex-col justify-between rounded-2xl">

      <div>
        {/* sidebar header section */}
        <div className="flex justify-center mt-5 gap-3">
          <div className="text-end font-bold text-gray-600 bg-white">MBRL <br />Admin Portal</div>
          <div className="flex items-center justify-center">
            <img src="/brandLogo.png" alt="Logo" className="h-10" />
          </div>
        </div>

        <div className="h-[1px] w-full bg-gradient-to-r from-transparent via-gray-300 to-transparent mb-5 mt-3" />

        {/* sidebar menu section */}
        <nav className="px-2 space-y-1">
          <SidebarItem icon={IoHome} label="Dashboard" to="/dashboard" />
          <SidebarItem icon={IoHome} label="Library" to="/dashboard" />
          <SidebarItem icon={IoHome} label="Analytics" to="/dashboard" />
          <SidebarItem icon={IoHome} label="Users" to="/dashboard" />
        </nav>

        <section className="text-center mt-10 text-gray-600 font-semibold">
          Account Pages
        </section>

        <div className="h-[1px] w-full bg-gradient-to-r from-transparent via-gray-300 to-transparent mb-5 mt-3" />

        <nav className="px-2 space-y-1">
          <SidebarItem icon={IoHome} label="Profile" to="/dashboard" />
          <SidebarItem icon={FaSignOutAlt} label="Log Out" to="/dashboard" />
        </nav>

      </div>

      <div className="p-4">
        <div
          className="w-full rounded-xl p-5 text-white overflow-hidden"
          style={{
            backgroundImage: "url('/footerCardBg.png')",
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        >
          <div className="flex flex-col gap-5">
            <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
              <span className="text-[#FF5B00] font-bold text-xl select-none">?</span>
            </div>

            <div>
              <h6 className="font-bold text-lg">Need help?</h6>
              <p className="text-xs">Please check our docs</p>
            </div>

            <button className="mt-auto bg-white text-gray-900 font-bold text-xs rounded-lg py-2 w-full">
              DOCUMENTATION
            </button>
          </div>
        </div>
      </div>

    </div>
  );
};

export default Sidebar;
