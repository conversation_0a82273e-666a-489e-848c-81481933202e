├── .env.local
├── .env.production
├── .gitignore
├── app
│   ├── (app)
│   │   ├── home
│   │   │   └── page.jsx
│   │   └── layout.jsx
│   ├── (auth)
│   ├── favicon.ico
│   ├── layout.js
│   └── page.js
├── clean-folder-structure.txt
├── components
│   ├── Button.jsx
│   ├── Header.jsx
│   ├── Modal.jsx
│   ├── Sidebar.jsx
│   └── Spinner.jsx
├── config
│   └── axiosConfig.js
├── constants
│   └── routes.js
├── hooks
├── jsconfig.json
├── lib
│   ├── api.js
│   ├── auth.js
│   └── formatters.js
├── middleware.js
├── next.config.mjs
├── package-lock.json
├── package.json
├── postcss.config.mjs
├── public
│   ├── file.svg
│   ├── globe.svg
│   ├── next.svg
│   ├── vercel.svg
│   └── window.svg
├── README.md
├── styles
│   ├── globals.css
│   └── tailwind.css
└── test.js
